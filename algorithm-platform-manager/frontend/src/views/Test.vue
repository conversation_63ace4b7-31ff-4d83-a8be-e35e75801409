<template>
  <div class="test-page">
    <!-- 算法选择 -->
    <el-card v-if="!selectedAlgorithm">
      <template #header>
        <div class="card-header">
          <span>🧪 在线测试</span>
          <el-button type="primary" @click="refreshAlgorithms" :loading="loading">
            刷新算法列表
          </el-button>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>

      <div v-else-if="algorithms.length === 0" class="empty-container">
        <el-empty description="暂无可测试的算法">
          <el-button type="primary" @click="refreshAlgorithms">刷新列表</el-button>
        </el-empty>
      </div>

      <div v-else class="algorithms-grid">
        <el-card 
          v-for="algorithm in runningAlgorithms" 
          :key="algorithm.id"
          class="algorithm-card"
          :class="{ 'selected': selectedAlgorithm?.id === algorithm.id }"
          @click="selectAlgorithm(algorithm)"
          shadow="hover"
        >
          <div class="algorithm-info">
            <div class="algorithm-header">
              <h3>{{ algorithm.name }}</h3>
              <el-tag :type="algorithm.status === 'running' ? 'success' : 'danger'" size="small">
                {{ algorithm.status === 'running' ? '运行中' : '已停止' }}
              </el-tag>
            </div>
            <p class="algorithm-description">{{ algorithm.description }}</p>
            <div class="algorithm-details">
              <span><strong>版本:</strong> {{ algorithm.version }}</span>
              <span><strong>类型:</strong> {{ algorithm.algorithm_type }}</span>
              <span><strong>端口:</strong> {{ algorithm.ports || '未配置' }}</span>
            </div>
          </div>
          <div class="algorithm-actions">
            <el-button type="primary" size="small">
              选择测试
            </el-button>
          </div>
        </el-card>
      </div>
    </el-card>

    <!-- 算法测试界面 -->
    <div v-else class="test-interface">
      <!-- 算法信息头部 -->
      <el-card class="algorithm-header-card">
        <div class="algorithm-header-info">
          <div class="algorithm-basic-info">
            <h2>{{ selectedAlgorithm.name }}</h2>
            <p>{{ selectedAlgorithm.description }}</p>
            <div class="algorithm-meta">
              <el-tag type="success" size="small">{{ selectedAlgorithm.status === 'running' ? '运行中' : '已停止' }}</el-tag>
              <span>版本: {{ selectedAlgorithm.version }}</span>
              <span>类型: {{ selectedAlgorithm.algorithm_type }}</span>
            </div>
          </div>
          <div class="algorithm-actions">
            <el-button @click="goBack">
              返回选择
            </el-button>
            <el-button type="primary" @click="refreshFunctions" :loading="functionsLoading">
              刷新功能
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 功能列表和测试区域 -->
      <div class="test-content">
        <!-- 功能列表 -->
        <el-card class="functions-card">
          <template #header>
            <span>🔧 可用功能</span>
          </template>

          <div v-if="functionsLoading" class="loading-container">
            <el-skeleton :rows="2" animated />
          </div>

          <div v-else-if="algorithmFunctions.length === 0" class="empty-container">
            <el-empty description="暂无可用功能" size="small">
              <el-button type="primary" size="small" @click="refreshFunctions">刷新功能</el-button>
            </el-empty>
          </div>

          <div v-else class="functions-list">
            <div 
              v-for="func in algorithmFunctions" 
              :key="func.name"
              class="function-item"
              :class="{ 'active': selectedFunction?.name === func.name }"
              @click="selectFunction(func)"
            >
              <div class="function-info">
                <h4>{{ func.name }}</h4>
                <p>{{ func.description }}</p>
                <div class="function-meta">
                  <el-tag size="small">{{ func.method }}</el-tag>
                  <span>{{ func.path }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 测试区域 -->
        <el-card class="test-area-card">
          <template #header>
            <span>🚀 功能测试</span>
          </template>

          <div v-if="!selectedFunction" class="empty-container">
            <el-empty description="请选择要测试的功能" size="small" />
          </div>

          <div v-else class="test-form">
            <h3>{{ selectedFunction.name }}</h3>
            <p>{{ selectedFunction.description }}</p>

            <!-- 参数输入 -->
            <div class="parameters-section">
              <h4>参数配置</h4>
              <div v-if="selectedFunction.parameters && selectedFunction.parameters.length > 0">
                <div 
                  v-for="param in selectedFunction.parameters" 
                  :key="param.name"
                  class="parameter-item"
                >
                  <label>{{ param.name }} <span v-if="param.required" class="required">*</span></label>
                  <el-input 
                    v-if="param.type === 'string' || param.type === 'text'"
                    v-model="testParams[param.name]"
                    :placeholder="param.description"
                    :required="param.required"
                  />
                  <el-input-number 
                    v-else-if="param.type === 'number' || param.type === 'integer'"
                    v-model="testParams[param.name]"
                    :placeholder="param.description"
                    :required="param.required"
                  />
                  <el-upload
                    v-else-if="param.type === 'file'"
                    class="upload-demo"
                    :auto-upload="false"
                    :on-change="(file) => handleFileChange(param.name, file)"
                    :show-file-list="false"
                  >
                    <el-button size="small" type="primary">选择文件</el-button>
                    <template #tip>
                      <div class="el-upload__tip">{{ param.description }}</div>
                    </template>
                  </el-upload>
                  <el-input 
                    v-else
                    v-model="testParams[param.name]"
                    :placeholder="param.description"
                    :required="param.required"
                  />
                  <small>{{ param.description }}</small>
                </div>
              </div>
              <div v-else>
                <p>此功能无需参数</p>
              </div>
            </div>

            <!-- 测试按钮 -->
            <div class="test-actions">
              <el-button
                type="primary"
                @click="executeTest"
                :loading="testLoading"
                :disabled="!canExecuteTest"
              >
                执行测试
              </el-button>
              <el-button @click="clearParams">
                清空参数
              </el-button>
            </div>

            <!-- 测试结果 -->
            <div v-if="testResult" class="test-result">
              <h4>测试结果</h4>
              <div class="result-header">
                <el-tag :type="testResult.success ? 'success' : 'danger'">
                  {{ testResult.success ? '成功' : '失败' }}
                </el-tag>
                <span class="result-time">{{ testResult.timestamp }}</span>
              </div>
              <div class="result-content">
                <pre>{{ JSON.stringify(testResult.data, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 暂时不导入图标，使用文本替代
// import {
//   Refresh as RefreshIcon,
//   Play as PlayIcon,
//   ArrowLeft as ArrowLeftIcon
// } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const functionsLoading = ref(false)
const testLoading = ref(false)
const algorithms = ref([])
const selectedAlgorithm = ref(null)
const algorithmFunctions = ref([])
const selectedFunction = ref(null)
const testParams = ref({})
const testResult = ref(null)

// 计算属性
const runningAlgorithms = computed(() => {
  return algorithms.value.filter(algo => algo.status === 'running')
})

const canExecuteTest = computed(() => {
  if (!selectedFunction.value) return false
  
  // 检查必需参数是否都已填写
  if (selectedFunction.value.parameters) {
    return selectedFunction.value.parameters
      .filter(param => param.required)
      .every(param => testParams.value[param.name])
  }
  
  return true
})

// 方法
const refreshAlgorithms = async () => {
  loading.value = true
  try {
    const response = await fetch('/api/algorithms/')
    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        algorithms.value = data.algorithms
      } else {
        ElMessage.error(data.message || '获取算法列表失败')
      }
    }
  } catch (error) {
    console.error('获取算法列表失败:', error)
    ElMessage.error('获取算法列表失败')
  } finally {
    loading.value = false
  }
}

const selectAlgorithm = async (algorithm) => {
  selectedAlgorithm.value = algorithm
  await refreshFunctions()
}

const goBack = () => {
  selectedAlgorithm.value = null
  selectedFunction.value = null
  algorithmFunctions.value = []
  testParams.value = {}
  testResult.value = null
}

const refreshFunctions = async () => {
  if (!selectedAlgorithm.value) return
  
  functionsLoading.value = true
  try {
    const response = await fetch(`/api/algorithms/${selectedAlgorithm.value.id}/functions`)
    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        algorithmFunctions.value = data.functions
      } else {
        ElMessage.error(data.message || '获取算法功能失败')
      }
    }
  } catch (error) {
    console.error('获取算法功能失败:', error)
    ElMessage.error('获取算法功能失败')
  } finally {
    functionsLoading.value = false
  }
}

const selectFunction = (func) => {
  selectedFunction.value = func
  testParams.value = {}
  testResult.value = null
  
  // 初始化参数默认值
  if (func.parameters) {
    func.parameters.forEach(param => {
      if (param.default !== undefined) {
        testParams.value[param.name] = param.default
      }
    })
  }
}

const handleFileChange = (paramName, file) => {
  testParams.value[paramName] = file.raw
}

const clearParams = () => {
  testParams.value = {}
  testResult.value = null
}

const executeTest = async () => {
  if (!selectedAlgorithm.value || !selectedFunction.value) return
  
  testLoading.value = true
  testResult.value = null
  
  try {
    const formData = new FormData()
    
    // 添加参数到FormData
    Object.keys(testParams.value).forEach(key => {
      const value = testParams.value[key]
      if (value instanceof File) {
        formData.append(key, value)
      } else {
        formData.append(key, JSON.stringify(value))
      }
    })
    
    const response = await fetch(
      `/api/algorithms/${selectedAlgorithm.value.id}/test/${selectedFunction.value.name}`,
      {
        method: 'POST',
        body: formData
      }
    )
    
    const data = await response.json()
    
    testResult.value = {
      success: response.ok && data.success,
      data: data,
      timestamp: new Date().toLocaleString()
    }
    
    if (testResult.value.success) {
      ElMessage.success('测试执行成功')
    } else {
      ElMessage.error(data.message || '测试执行失败')
    }
    
  } catch (error) {
    console.error('测试执行失败:', error)
    testResult.value = {
      success: false,
      data: { error: error.message },
      timestamp: new Date().toLocaleString()
    }
    ElMessage.error('测试执行失败')
  } finally {
    testLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  refreshAlgorithms()
})
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading-container, .empty-container {
  padding: 40px;
  text-align: center;
}

.algorithms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.algorithm-card {
  cursor: pointer;
  transition: all 0.3s;
}

.algorithm-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.algorithm-card.selected {
  border-color: #409eff;
}

.algorithm-info {
  margin-bottom: 15px;
}

.algorithm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.algorithm-header h3 {
  margin: 0;
  color: #303133;
}

.algorithm-description {
  color: #606266;
  margin: 10px 0;
  font-size: 14px;
}

.algorithm-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 12px;
  color: #909399;
}

.algorithm-actions {
  text-align: center;
}

.test-interface {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.algorithm-header-card {
  margin-bottom: 20px;
}

.algorithm-header-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.algorithm-basic-info h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.algorithm-meta {
  display: flex;
  gap: 15px;
  align-items: center;
  margin-top: 10px;
}

.test-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 20px;
}

.functions-card {
  height: fit-content;
}

.functions-list {
  max-height: 600px;
  overflow-y: auto;
}

.function-item {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.function-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.function-item.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.function-info h4 {
  margin: 0 0 5px 0;
  color: #303133;
}

.function-info p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.function-meta {
  display: flex;
  gap: 10px;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.test-form {
  padding: 20px;
}

.test-form h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.parameters-section {
  margin: 20px 0;
}

.parameters-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.parameter-item {
  margin-bottom: 20px;
}

.parameter-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #303133;
}

.required {
  color: #f56c6c;
}

.parameter-item small {
  display: block;
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
}

.test-actions {
  margin: 30px 0;
  display: flex;
  gap: 10px;
}

.test-result {
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fafafa;
}

.test-result h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.result-time {
  color: #909399;
  font-size: 14px;
}

.result-content {
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
}

.result-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #303133;
}
</style>
