# 温州人脸识别算法 (Wenzhou Face Recognition)

## 🎯 项目概述

这是一个基于深度学习的人脸识别算法，支持人脸检测、人脸特征提取和人脸比对。该项目提供完整的本地开发和 Docker 部署解决方案。

## 📁 项目结构

```
wenzhou_face/
├── 📂 dev/                          # 开发环境
│   ├── inference_engine.py          # 推理引擎
│   ├── run_inference.py             # 推理脚本
│   ├── logger_config.py             # 日志配置
│   ├── config.ini                   # 配置文件
│   ├── models/                      # 模型文件
│   │   ├── face_detect.onnx         # 人脸检测模型
│   │   ├── face_feature.onnx        # 人脸特征提取模型
│   │   └── face_quality.onnx        # 人脸质量评估模型
│   └── utils/                       # 工具模块
├── 📂 docker/                       # Docker 部署
│   ├── Dockerfile                   # Docker 镜像配置
│   ├── requirements.txt             # Python 依赖
│   └── (同步的代码文件)
├── 📂 scripts/                      # 自动化脚本
│   ├── sync_to_docker.sh            # 同步到 Docker
│   ├── build_docker.sh              # 构建 Docker 镜像
│   ├── deploy.sh                    # 一键部署
│   └── quick_start.sh               # 快速开始
├── 📂 data/                         # 数据目录
│   ├── input/                       # 输入图像
│   └── output/                      # 输出结果
├── 📂 logs/                         # 日志目录
├── pyproject.toml                   # uv 项目配置
└── README.md                        # 项目说明 (本文件)
```

## 🚀 快速开始

### 方式一：交互式菜单 (推荐)
```bash
./scripts/quick_start.sh
```

### 方式二：命令行操作

#### 1. 本地开发
```bash
# 进入开发目录
cd dev

# 配置授权密钥 (编辑 config.ini)
vim config.ini

# 运行推理
uv run python run_inference.py ../data/input/your_image.jpg

# 查看结果
ls ../data/output/
```

#### 2. Docker 部署

##### 构建镜像
```bash
# 构建Docker镜像
docker build -t wenzhou-face-api:latest .
```

##### 启动API服务
```bash
# 方法1: 使用通用启动脚本（推荐）
./scripts/start_universal.sh

# 方法2: 直接使用Docker命令
docker run -d \
  --name wenzhou-face-test \
  --add-host=host.docker.internal:host-gateway \
  -p 8001:8001 \
  -e "IN_DOCKER=1" \
  wenzhou-face-api:latest

# 方法3: 指定授权服务地址
docker run -d \
  --name wenzhou-face-test \
  --add-host=host.docker.internal:host-gateway \
  -p 8001:8001 \
  -e "IN_DOCKER=1" \
  -e "LICENSE_SERVER_URL=http://your-server:8000/verify" \
  wenzhou-face-api:latest
```

##### 测试API服务
```bash
# 健康检查
curl http://localhost:8001/api/v1/health

# 算法信息
curl http://localhost:8001/api/v1/info

# API文档
open http://localhost:8001/docs
```

#### 3. 一键操作
```bash
# 一键重新部署
./scripts/deploy.sh --rebuild

# 查看容器状态
./scripts/deploy.sh --status

# 查看日志
./scripts/deploy.sh --logs
```

## ⚙️ 配置说明

### 授权配置 (config.ini)
```ini
[LICENSE]
key = YOUR_UNIQUE_LICENSE_KEY_FOR_THIS_CLIENT

[FACE_DETECTION]
confidence_threshold = 0.7
nms_threshold = 0.4
input_size = 640

[FACE_RECOGNITION]
feature_dim = 512
similarity_threshold = 0.6

[FACE_QUALITY]
enable_quality_check = true
min_quality_score = 0.5
```

### 环境变量
- `IN_DOCKER`: 自动检测是否在 Docker 环境中运行
- 本地环境：自动使用相对路径
- Docker 环境：自动使用容器内路径

## 📊 功能特性

### 人脸检测
- ✅ **高精度检测** - 基于SCRFD模型的人脸检测
- ✅ **关键点定位** - 5点人脸关键点检测
- ✅ **多尺度检测** - 支持不同尺寸的人脸检测

### 人脸识别
- ✅ **特征提取** - 基于ResNet50的人脸特征提取
- ✅ **人脸比对** - 高精度人脸相似度计算
- ✅ **批量处理** - 支持批量人脸识别

### 人脸质量评估
- ✅ **质量评分** - 基于PFLD的人脸质量评估
- ✅ **角度检测** - 人脸姿态角度检测
- ✅ **模糊检测** - 人脸清晰度评估

### 输入支持
- ✅ **单张图像** - JPG, PNG, JPEG 格式
- ✅ **批量图像** - 目录批量处理
- ✅ **Base64编码** - 支持Base64图像输入

### 输出格式
- ✅ **可视化结果** - 带标注框的图像
- ✅ **结构化数据** - JSON 格式检测结果
- ✅ **特征向量** - 人脸特征向量输出

### 日志系统
- ✅ **多级日志** - DEBUG, INFO, WARNING, ERROR
- ✅ **性能监控** - 处理时间、检测数量统计
- ✅ **日志轮转** - 自动管理日志文件大小
- ✅ **环境适配** - 本地和 Docker 环境自动切换

## 🛠️ 开发指南

### 本地开发环境

#### 通用安装
```bash
# 安装 uv (如果未安装)
curl -LsSf https://astral.sh/uv/install.sh | sh

# 进入开发目录
cd src

# 安装依赖
uv sync

# 运行测试
uv run python run_inference.py ../data/input/test.jpg
```

#### Mac M3 优化安装 🍎
```bash
# 一键优化安装（推荐）
chmod +x install_mac_m3.sh
./install_mac_m3.sh

# 运行兼容性测试
python test_mac_m3.py

# 手动安装 Apple Silicon 优化版本
pip uninstall onnxruntime  # 移除标准版本
pip install onnxruntime-silicon  # 安装优化版本
```

### 代码修改流程
1. **本地开发** - 在 `dev/` 目录中修改代码
2. **本地测试** - 验证功能正常
3. **同步代码** - `./scripts/sync_to_docker.sh`
4. **构建镜像** - `./scripts/build_docker.sh`
5. **部署测试** - `./scripts/deploy.sh`

### 添加新功能
1. 修改 `dev/inference_engine.py` - 核心算法逻辑
2. 修改 `dev/run_inference.py` - 接口和流程
3. 更新 `dev/config.ini` - 配置参数
4. 测试并同步到 Docker

## 📋 系统要求

### 本地开发
- **操作系统**: macOS, Linux, Windows
- **Python**: 3.8+
- **包管理器**: uv (推荐) 或 pip
- **GPU**: 可选，支持 CUDA 和 MPS

#### Mac M3 特别说明 🍎
- **Apple Silicon**: 原生支持 M1/M2/M3 芯片
- **CoreML 加速**: 自动启用 Apple 的机器学习加速
- **优化安装**: 使用 `./install_mac_m3.sh` 进行优化安装
- **性能测试**: 运行 `python test_mac_m3.py` 检查环境

### Docker 部署
- **Docker**: 20.10+
- **内存**: 4GB+
- **存储**: 10GB+
- **注意**: Docker 版本为纯 CPU 运行，不支持 GPU 加速

## 🔧 故障排除

### 常见问题

**1. 授权验证失败**
```bash
# 检查配置文件
cat dev/config.ini

# 检查网络连接
curl -X POST http://127.0.0.1:8000/verify -H "Content-Type: application/json" -d '{"license_key":"your_key"}'
```

**2. 模型加载失败**
```bash
# 检查模型文件
ls -la dev/models/

# 检查 ONNX Runtime 版本
python -c "import onnxruntime; print(onnxruntime.__version__)"
```

**3. Docker 构建失败**
```bash
# 清理 Docker 缓存
docker system prune -f

# 重新构建
./scripts/build_docker.sh --no-cache
```

### 日志查看
```bash
# 本地日志
ls logs/
tail -f logs/wenzhou_face_detailed.log

# Docker 日志
./scripts/deploy.sh --logs
```

## 📈 性能优化

### 本地环境
- **MPS 加速** (Apple Silicon): 自动启用
- **CUDA 加速** (NVIDIA GPU): 自动检测
- **CPU 优化**: 多线程处理

### Docker 环境
- **内存优化**: 合理设置容器内存限制
- **存储优化**: 使用 volume 挂载避免数据丢失
- **网络优化**: 独立网络避免端口冲突

## 📞 技术支持

如有问题，请查看：
1. **日志文件** - `logs/` 目录下的详细日志
2. **配置检查** - 确认 `config.ini` 配置正确
3. **环境验证** - 运行 `./scripts/quick_start.sh` 进行诊断

---

**这是一个完全独立的算法包，可以单独复制给其他人使用！** 🚀
