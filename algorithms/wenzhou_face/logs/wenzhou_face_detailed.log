2025-07-24 22:41:57 | wenzhou_face | INFO | logger_config.py:157 | 授权验证成功: License有效
2025-07-24 22:41:57 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.024s
2025-07-24 22:41:58 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 0.203s
2025-07-24 22:41:58 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceQuality (models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx) | 耗时: 0.015s
2025-07-24 22:41:58 | wenzhou_face | INFO | inference_engine.py:655 | 温州人脸识别引擎初始化完成
2025-07-24 22:41:58 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face1.jpg
2025-07-24 22:41:58 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face1.jpg | 检测到 1 张人脸 | 耗时: 0.422s
2025-07-24 22:41:58 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face1.jpg | detect_faces=1 | detect_time=0.422
2025-07-24 22:41:58 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face1.jpg | 检测人脸数: 1 | 耗时: 0.567s
2025-07-24 22:41:58 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face1.jpg | faces=1 | time=0.567
2025-07-24 22:41:58 | wenzhou_face | INFO | inference_engine.py:837 | 结果已保存到: ../data/output/face1_results.json
2025-07-24 22:42:25 | wenzhou_face | INFO | logger_config.py:157 | 授权验证成功: License有效
2025-07-24 22:42:25 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.038s
2025-07-24 22:42:25 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 0.135s
2025-07-24 22:42:25 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceQuality (models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx) | 耗时: 0.007s
2025-07-24 22:42:25 | wenzhou_face | INFO | inference_engine.py:655 | 温州人脸识别引擎初始化完成
2025-07-24 22:42:25 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face8.jpg
2025-07-24 22:42:25 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face8.jpg | 检测到 2 张人脸 | 耗时: 0.099s
2025-07-24 22:42:25 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face8.jpg | detect_faces=2 | detect_time=0.099
2025-07-24 22:42:25 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face8.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:25 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face8.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:25 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face1.jpg
2025-07-24 22:42:25 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face1.jpg | 检测到 1 张人脸 | 耗时: 0.090s
2025-07-24 22:42:25 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face1.jpg | detect_faces=1 | detect_time=0.090
2025-07-24 22:42:25 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face1.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:25 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face1.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:25 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face2.jpg
2025-07-24 22:42:26 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face2.jpg | 检测到 0 张人脸 | 耗时: 0.134s
2025-07-24 22:42:26 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face2.jpg | detect_faces=0 | detect_time=0.134
2025-07-24 22:42:26 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face2.jpg | 检测人脸数: 0 | 耗时: 0.136s
2025-07-24 22:42:26 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face2.jpg | faces=0 | time=0.136
2025-07-24 22:42:26 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face3.jpg
2025-07-24 22:42:26 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face3.jpg | 检测到 1 张人脸 | 耗时: 0.100s
2025-07-24 22:42:26 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face3.jpg | detect_faces=1 | detect_time=0.100
2025-07-24 22:42:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face3.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face3.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face7.jpg
2025-07-24 22:42:26 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face7.jpg | 检测到 15 张人脸 | 耗时: 0.090s
2025-07-24 22:42:26 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face7.jpg | detect_faces=15 | detect_time=0.090
2025-07-24 22:42:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face7.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face7.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face6.jpg
2025-07-24 22:42:26 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face6.jpg | 检测到 3 张人脸 | 耗时: 0.088s
2025-07-24 22:42:26 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face6.jpg | detect_faces=3 | detect_time=0.088
2025-07-24 22:42:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face6.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face6.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face4.jpg
2025-07-24 22:42:26 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face4.jpg | 检测到 1 张人脸 | 耗时: 0.087s
2025-07-24 22:42:26 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face4.jpg | detect_faces=1 | detect_time=0.087
2025-07-24 22:42:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face4.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face4.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face5.jpg
2025-07-24 22:42:26 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face5.jpg | 检测到 1 张人脸 | 耗时: 0.086s
2025-07-24 22:42:26 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face5.jpg | detect_faces=1 | detect_time=0.086
2025-07-24 22:42:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face5.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face5.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:42:26 | wenzhou_face | INFO | logger_config.py:167 | 批量处理完成: 总数=8 | 成功=1 | 失败=7 | 总耗时=1.217s
2025-07-24 22:42:26 | wenzhou_face.performance | INFO | logger_config.py:171 | batch_total=8 | batch_success=1 | batch_failed=7 | batch_time=1.217
2025-07-24 22:42:26 | wenzhou_face | INFO | inference_engine.py:837 | 结果已保存到: ../data/output/batch_results.json
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:157 | 授权验证成功: License有效
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.030s
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 0.091s
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceQuality (models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx) | 耗时: 0.007s
2025-07-24 22:42:40 | wenzhou_face | INFO | inference_engine.py:655 | 温州人脸识别引擎初始化完成
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face8.jpg
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face8.jpg | 检测到 2 张人脸 | 耗时: 0.096s
2025-07-24 22:42:40 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face8.jpg | detect_faces=2 | detect_time=0.096
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face8.jpg | 检测人脸数: 2 | 耗时: 0.192s
2025-07-24 22:42:40 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face8.jpg | faces=2 | time=0.192
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face1.jpg
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face1.jpg | 检测到 1 张人脸 | 耗时: 0.085s
2025-07-24 22:42:40 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face1.jpg | detect_faces=1 | detect_time=0.085
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face1.jpg | 检测人脸数: 1 | 耗时: 0.130s
2025-07-24 22:42:40 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face1.jpg | faces=1 | time=0.130
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face2.jpg
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face2.jpg | 检测到 0 张人脸 | 耗时: 0.086s
2025-07-24 22:42:40 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face2.jpg | detect_faces=0 | detect_time=0.086
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face2.jpg | 检测人脸数: 0 | 耗时: 0.087s
2025-07-24 22:42:40 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face2.jpg | faces=0 | time=0.087
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face3.jpg
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face3.jpg | 检测到 1 张人脸 | 耗时: 0.142s
2025-07-24 22:42:40 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face3.jpg | detect_faces=1 | detect_time=0.142
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face3.jpg | 检测人脸数: 1 | 耗时: 0.213s
2025-07-24 22:42:40 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face3.jpg | faces=1 | time=0.213
2025-07-24 22:42:40 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face7.jpg
2025-07-24 22:42:41 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face7.jpg | 检测到 15 张人脸 | 耗时: 0.088s
2025-07-24 22:42:41 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face7.jpg | detect_faces=15 | detect_time=0.088
2025-07-24 22:42:41 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face7.jpg | 检测人脸数: 15 | 耗时: 0.744s
2025-07-24 22:42:41 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face7.jpg | faces=15 | time=0.744
2025-07-24 22:42:41 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face6.jpg
2025-07-24 22:42:41 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face6.jpg | 检测到 3 张人脸 | 耗时: 0.086s
2025-07-24 22:42:41 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face6.jpg | detect_faces=3 | detect_time=0.086
2025-07-24 22:42:41 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face6.jpg | 检测人脸数: 3 | 耗时: 0.228s
2025-07-24 22:42:41 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face6.jpg | faces=3 | time=0.228
2025-07-24 22:42:41 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face4.jpg
2025-07-24 22:42:42 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face4.jpg | 检测到 1 张人脸 | 耗时: 0.093s
2025-07-24 22:42:42 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face4.jpg | detect_faces=1 | detect_time=0.093
2025-07-24 22:42:42 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face4.jpg | 检测人脸数: 1 | 耗时: 0.140s
2025-07-24 22:42:42 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face4.jpg | faces=1 | time=0.140
2025-07-24 22:42:42 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face5.jpg
2025-07-24 22:42:42 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face5.jpg | 检测到 1 张人脸 | 耗时: 0.088s
2025-07-24 22:42:42 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face5.jpg | detect_faces=1 | detect_time=0.088
2025-07-24 22:42:42 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face5.jpg | 检测人脸数: 1 | 耗时: 0.134s
2025-07-24 22:42:42 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face5.jpg | faces=1 | time=0.134
2025-07-24 22:42:42 | wenzhou_face | INFO | logger_config.py:167 | 批量处理完成: 总数=8 | 成功=8 | 失败=0 | 总耗时=1.870s
2025-07-24 22:42:42 | wenzhou_face.performance | INFO | logger_config.py:171 | batch_total=8 | batch_success=8 | batch_failed=0 | batch_time=1.870
2025-07-24 22:42:42 | wenzhou_face | INFO | inference_engine.py:837 | 结果已保存到: ../data/output/batch_results.json
2025-07-24 22:43:01 | wenzhou_face | INFO | logger_config.py:157 | 授权验证成功: License有效
2025-07-24 22:43:01 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.011s
2025-07-24 22:43:02 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 0.083s
2025-07-24 22:43:02 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceQuality (models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx) | 耗时: 0.006s
2025-07-24 22:43:02 | wenzhou_face | INFO | inference_engine.py:655 | 温州人脸识别引擎初始化完成
2025-07-24 22:43:02 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face1.jpg
2025-07-24 22:43:02 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face1.jpg | 检测到 1 张人脸 | 耗时: 0.096s
2025-07-24 22:43:02 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face1.jpg | detect_faces=1 | detect_time=0.096
2025-07-24 22:43:02 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face1.jpg | 检测人脸数: 1 | 耗时: 0.157s
2025-07-24 22:43:02 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face1.jpg | faces=1 | time=0.157
2025-07-24 22:43:02 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face3.jpg
2025-07-24 22:43:02 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face3.jpg | 检测到 1 张人脸 | 耗时: 0.088s
2025-07-24 22:43:02 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face3.jpg | detect_faces=1 | detect_time=0.088
2025-07-24 22:43:02 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face3.jpg | 检测人脸数: 1 | 耗时: 0.145s
2025-07-24 22:43:02 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face3.jpg | faces=1 | time=0.145
2025-07-24 22:47:25 | wenzhou_face | INFO | logger_config.py:157 | 授权验证成功: License有效
2025-07-24 22:47:25 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.030s
2025-07-24 22:47:25 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 0.094s
2025-07-24 22:47:25 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceQuality (models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx) | 耗时: 0.007s
2025-07-24 22:47:25 | wenzhou_face | INFO | inference_engine.py:655 | 温州人脸识别引擎初始化完成
2025-07-24 22:47:25 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face8.jpg
2025-07-24 22:47:25 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face8.jpg | 检测到 2 张人脸 | 耗时: 0.107s
2025-07-24 22:47:25 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face8.jpg | detect_faces=2 | detect_time=0.107
2025-07-24 22:47:25 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face8.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:25 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face8.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:25 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face1.jpg
2025-07-24 22:47:25 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face1.jpg | 检测到 1 张人脸 | 耗时: 0.089s
2025-07-24 22:47:25 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face1.jpg | detect_faces=1 | detect_time=0.089
2025-07-24 22:47:25 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face1.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:25 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face1.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:25 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face2.jpg
2025-07-24 22:47:25 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face2.jpg | 检测到 0 张人脸 | 耗时: 0.085s
2025-07-24 22:47:25 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face2.jpg | detect_faces=0 | detect_time=0.085
2025-07-24 22:47:25 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face2.jpg | 检测人脸数: 0 | 耗时: 0.087s
2025-07-24 22:47:25 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face2.jpg | faces=0 | time=0.087
2025-07-24 22:47:25 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face3.jpg
2025-07-24 22:47:26 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face3.jpg | 检测到 1 张人脸 | 耗时: 0.085s
2025-07-24 22:47:26 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face3.jpg | detect_faces=1 | detect_time=0.085
2025-07-24 22:47:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face3.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face3.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face7.jpg
2025-07-24 22:47:26 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face7.jpg | 检测到 15 张人脸 | 耗时: 0.086s
2025-07-24 22:47:26 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face7.jpg | detect_faces=15 | detect_time=0.086
2025-07-24 22:47:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face7.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face7.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face6.jpg
2025-07-24 22:47:26 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face6.jpg | 检测到 3 张人脸 | 耗时: 0.090s
2025-07-24 22:47:26 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face6.jpg | detect_faces=3 | detect_time=0.090
2025-07-24 22:47:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face6.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face6.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face4.jpg
2025-07-24 22:47:26 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face4.jpg | 检测到 1 张人脸 | 耗时: 0.086s
2025-07-24 22:47:26 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face4.jpg | detect_faces=1 | detect_time=0.086
2025-07-24 22:47:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face4.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face4.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face5.jpg
2025-07-24 22:47:26 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face5.jpg | 检测到 1 张人脸 | 耗时: 0.084s
2025-07-24 22:47:26 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face5.jpg | detect_faces=1 | detect_time=0.084
2025-07-24 22:47:26 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face5.jpg: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 773, in process_image
    quality_info = self.face_quality_assessor.assess_quality(face_image)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 587, in assess_quality
    pose = self.pose_estimator.solve_pose_by_68_points(points_68)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 173, in solve_pose_by_68_points
    success, rotation_vector, translation_vector = cv2.solvePnP(
                                                   ~~~~~~~~~~~~^
        self.model_points_68,
        ^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        self.dist_coeffs
        ^^^^^^^^^^^^^^^^
    )
    ^
cv2.error: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | ERROR | inference_engine.py:816 | 处理图像失败: ../data/input/face5.jpg, 错误: OpenCV(4.11.0) /Users/<USER>/GHA-Actions-OpenCV/_work/opencv-python/opencv-python/opencv/modules/calib3d/src/solvepnp.cpp:824: error: (-215:Assertion failed) ( (npoints >= 4) || (npoints == 3 && flags == SOLVEPNP_ITERATIVE && useExtrinsicGuess) || (npoints >= 3 && flags == SOLVEPNP_SQPNP) ) && npoints == std::max(ipoints.checkVector(2, CV_32F), ipoints.checkVector(2, CV_64F)) in function 'solvePnPGeneric'

2025-07-24 22:47:26 | wenzhou_face | INFO | logger_config.py:167 | 批量处理完成: 总数=8 | 成功=1 | 失败=7 | 总耗时=1.098s
2025-07-24 22:47:26 | wenzhou_face.performance | INFO | logger_config.py:171 | batch_total=8 | batch_success=1 | batch_failed=7 | batch_time=1.098
2025-07-24 22:47:26 | wenzhou_face | INFO | inference_engine.py:837 | 结果已保存到: ../data/output/batch_results.json
2025-07-24 22:49:36 | wenzhou_face | INFO | logger_config.py:157 | 授权验证成功: License有效
2025-07-24 22:49:36 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.034s
2025-07-24 22:49:36 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 0.109s
2025-07-24 22:49:36 | wenzhou_face | INFO | inference_engine.py:655 | 温州人脸识别引擎初始化完成
2025-07-24 22:49:36 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face1.jpg
2025-07-24 22:49:36 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face1.jpg | 检测到 1 张人脸 | 耗时: 0.096s
2025-07-24 22:49:36 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face1.jpg | detect_faces=1 | detect_time=0.096
2025-07-24 22:49:36 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face1.jpg | 检测人脸数: 1 | 耗时: 0.148s
2025-07-24 22:49:36 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face1.jpg | faces=1 | time=0.148
2025-07-24 22:49:36 | wenzhou_face | INFO | inference_engine.py:837 | 结果已保存到: ../data/output/face1_results.json
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:157 | 授权验证成功: License有效
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.012s
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 0.101s
2025-07-24 22:49:49 | wenzhou_face | INFO | inference_engine.py:655 | 温州人脸识别引擎初始化完成
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face8.jpg
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face8.jpg | 检测到 2 张人脸 | 耗时: 0.097s
2025-07-24 22:49:49 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face8.jpg | detect_faces=2 | detect_time=0.097
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face8.jpg | 检测人脸数: 2 | 耗时: 0.187s
2025-07-24 22:49:49 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face8.jpg | faces=2 | time=0.187
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face1.jpg
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face1.jpg | 检测到 1 张人脸 | 耗时: 0.088s
2025-07-24 22:49:49 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face1.jpg | detect_faces=1 | detect_time=0.088
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face1.jpg | 检测人脸数: 1 | 耗时: 0.136s
2025-07-24 22:49:49 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face1.jpg | faces=1 | time=0.136
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face2.jpg
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face2.jpg | 检测到 0 张人脸 | 耗时: 0.089s
2025-07-24 22:49:49 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face2.jpg | detect_faces=0 | detect_time=0.089
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face2.jpg | 检测人脸数: 0 | 耗时: 0.091s
2025-07-24 22:49:49 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face2.jpg | faces=0 | time=0.091
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face3.jpg
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face3.jpg | 检测到 1 张人脸 | 耗时: 0.090s
2025-07-24 22:49:49 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face3.jpg | detect_faces=1 | detect_time=0.090
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face3.jpg | 检测人脸数: 1 | 耗时: 0.141s
2025-07-24 22:49:49 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face3.jpg | faces=1 | time=0.141
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face7.jpg
2025-07-24 22:49:49 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face7.jpg | 检测到 15 张人脸 | 耗时: 0.124s
2025-07-24 22:49:49 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face7.jpg | detect_faces=15 | detect_time=0.124
2025-07-24 22:49:50 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face7.jpg | 检测人脸数: 15 | 耗时: 0.845s
2025-07-24 22:49:50 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face7.jpg | faces=15 | time=0.845
2025-07-24 22:49:50 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face6.jpg
2025-07-24 22:49:50 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face6.jpg | 检测到 3 张人脸 | 耗时: 0.091s
2025-07-24 22:49:50 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face6.jpg | detect_faces=3 | detect_time=0.091
2025-07-24 22:49:50 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face6.jpg | 检测人脸数: 3 | 耗时: 0.225s
2025-07-24 22:49:50 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face6.jpg | faces=3 | time=0.225
2025-07-24 22:49:50 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face4.jpg
2025-07-24 22:49:51 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face4.jpg | 检测到 1 张人脸 | 耗时: 0.085s
2025-07-24 22:49:51 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face4.jpg | detect_faces=1 | detect_time=0.085
2025-07-24 22:49:51 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face4.jpg | 检测人脸数: 1 | 耗时: 0.138s
2025-07-24 22:49:51 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face4.jpg | faces=1 | time=0.138
2025-07-24 22:49:51 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face5.jpg
2025-07-24 22:49:51 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face5.jpg | 检测到 1 张人脸 | 耗时: 0.091s
2025-07-24 22:49:51 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face5.jpg | detect_faces=1 | detect_time=0.091
2025-07-24 22:49:51 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face5.jpg | 检测人脸数: 1 | 耗时: 0.143s
2025-07-24 22:49:51 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face5.jpg | faces=1 | time=0.143
2025-07-24 22:49:51 | wenzhou_face | INFO | logger_config.py:167 | 批量处理完成: 总数=8 | 成功=8 | 失败=0 | 总耗时=1.908s
2025-07-24 22:49:51 | wenzhou_face.performance | INFO | logger_config.py:171 | batch_total=8 | batch_success=8 | batch_failed=0 | batch_time=1.908
2025-07-24 22:49:51 | wenzhou_face | INFO | inference_engine.py:837 | 结果已保存到: ../data/output/batch_results.json
2025-07-24 22:50:04 | wenzhou_face | INFO | logger_config.py:157 | 授权验证成功: License有效
2025-07-24 22:50:04 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.014s
2025-07-24 22:50:04 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 0.094s
2025-07-24 22:50:04 | wenzhou_face | INFO | inference_engine.py:655 | 温州人脸识别引擎初始化完成
2025-07-24 22:50:04 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face1.jpg
2025-07-24 22:50:04 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face1.jpg | 检测到 1 张人脸 | 耗时: 0.088s
2025-07-24 22:50:04 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face1.jpg | detect_faces=1 | detect_time=0.088
2025-07-24 22:50:04 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face1.jpg | 检测人脸数: 1 | 耗时: 0.138s
2025-07-24 22:50:04 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face1.jpg | faces=1 | time=0.138
2025-07-24 22:50:04 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face3.jpg
2025-07-24 22:50:04 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face3.jpg | 检测到 1 张人脸 | 耗时: 0.087s
2025-07-24 22:50:04 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face3.jpg | detect_faces=1 | detect_time=0.087
2025-07-24 22:50:04 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face3.jpg | 检测人脸数: 1 | 耗时: 0.138s
2025-07-24 22:50:04 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face3.jpg | faces=1 | time=0.138
2025-07-24 23:03:02 | wenzhou_face | INFO | logger_config.py:157 | 授权验证成功: License有效
2025-07-24 23:03:02 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.031s
2025-07-24 23:03:02 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 0.101s
2025-07-24 23:03:02 | wenzhou_face | INFO | inference_engine.py:773 | 温州人脸识别引擎初始化完成
2025-07-24 23:03:02 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face1.jpg
2025-07-24 23:03:02 | wenzhou_face | ERROR | logger_config.py:150 | 图像处理失败: ../data/input/face1.jpg: 'FaceDetectionModel' object has no attribute 'preprocess'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 901, in process_image
    bboxes, landmarks = self.face_detector.detect(image_array)
                        ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/dev/inference_engine.py", line 336, in detect
    input_data, scale = self.preprocess(image)
                        ^^^^^^^^^^^^^^^
AttributeError: 'FaceDetectionModel' object has no attribute 'preprocess'
2025-07-24 23:03:02 | wenzhou_face | ERROR | run_inference.py:117 | 处理图像时发生错误: 'FaceDetectionModel' object has no attribute 'preprocess'
2025-07-24 23:03:47 | wenzhou_face | INFO | logger_config.py:157 | 授权验证成功: License有效
2025-07-24 23:03:47 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.028s
2025-07-24 23:03:47 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 0.115s
2025-07-24 23:03:47 | wenzhou_face | INFO | inference_engine.py:819 | 温州人脸识别引擎初始化完成
2025-07-24 23:03:47 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face1.jpg
2025-07-24 23:03:47 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face1.jpg | 检测到 1 张人脸 | 耗时: 0.097s
2025-07-24 23:03:47 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face1.jpg | detect_faces=1 | detect_time=0.097
2025-07-24 23:03:47 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face1.jpg | 检测人脸数: 1 | 耗时: 0.151s
2025-07-24 23:03:47 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face1.jpg | faces=1 | time=0.151
2025-07-24 23:03:47 | wenzhou_face | INFO | inference_engine.py:1043 | 结果已保存到: ../data/output/face1_results.json
2025-07-24 23:03:55 | wenzhou_face | INFO | logger_config.py:157 | 授权验证成功: License有效
2025-07-24 23:03:55 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.021s
2025-07-24 23:03:55 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 0.108s
2025-07-24 23:03:55 | wenzhou_face | INFO | inference_engine.py:819 | 温州人脸识别引擎初始化完成
2025-07-24 23:03:55 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face8.jpg
2025-07-24 23:03:56 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face8.jpg | 检测到 2 张人脸 | 耗时: 0.108s
2025-07-24 23:03:56 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face8.jpg | detect_faces=2 | detect_time=0.108
2025-07-24 23:03:56 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face8.jpg | 检测人脸数: 2 | 耗时: 0.199s
2025-07-24 23:03:56 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face8.jpg | faces=2 | time=0.199
2025-07-24 23:03:56 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face1.jpg
2025-07-24 23:03:56 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face1.jpg | 检测到 1 张人脸 | 耗时: 0.088s
2025-07-24 23:03:56 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face1.jpg | detect_faces=1 | detect_time=0.088
2025-07-24 23:03:56 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face1.jpg | 检测人脸数: 1 | 耗时: 0.133s
2025-07-24 23:03:56 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face1.jpg | faces=1 | time=0.133
2025-07-24 23:03:56 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face2.jpg
2025-07-24 23:03:56 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face2.jpg | 检测到 0 张人脸 | 耗时: 0.088s
2025-07-24 23:03:56 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face2.jpg | detect_faces=0 | detect_time=0.088
2025-07-24 23:03:56 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face2.jpg | 检测人脸数: 0 | 耗时: 0.089s
2025-07-24 23:03:56 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face2.jpg | faces=0 | time=0.089
2025-07-24 23:03:56 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face3.jpg
2025-07-24 23:03:56 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face3.jpg | 检测到 1 张人脸 | 耗时: 0.118s
2025-07-24 23:03:56 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face3.jpg | detect_faces=1 | detect_time=0.118
2025-07-24 23:03:56 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face3.jpg | 检测人脸数: 1 | 耗时: 0.194s
2025-07-24 23:03:56 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face3.jpg | faces=1 | time=0.194
2025-07-24 23:03:56 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face7.jpg
2025-07-24 23:03:56 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face7.jpg | 检测到 15 张人脸 | 耗时: 0.117s
2025-07-24 23:03:56 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face7.jpg | detect_faces=15 | detect_time=0.117
2025-07-24 23:03:57 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face7.jpg | 检测人脸数: 15 | 耗时: 0.768s
2025-07-24 23:03:57 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face7.jpg | faces=15 | time=0.768
2025-07-24 23:03:57 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face6.jpg
2025-07-24 23:03:57 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face6.jpg | 检测到 3 张人脸 | 耗时: 0.086s
2025-07-24 23:03:57 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face6.jpg | detect_faces=3 | detect_time=0.086
2025-07-24 23:03:57 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face6.jpg | 检测人脸数: 3 | 耗时: 0.217s
2025-07-24 23:03:57 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face6.jpg | faces=3 | time=0.217
2025-07-24 23:03:57 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face4.jpg
2025-07-24 23:03:57 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face4.jpg | 检测到 1 张人脸 | 耗时: 0.086s
2025-07-24 23:03:57 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face4.jpg | detect_faces=1 | detect_time=0.086
2025-07-24 23:03:57 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face4.jpg | 检测人脸数: 1 | 耗时: 0.135s
2025-07-24 23:03:57 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face4.jpg | faces=1 | time=0.135
2025-07-24 23:03:57 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face5.jpg
2025-07-24 23:03:57 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face5.jpg | 检测到 1 张人脸 | 耗时: 0.091s
2025-07-24 23:03:57 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face5.jpg | detect_faces=1 | detect_time=0.091
2025-07-24 23:03:57 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face5.jpg | 检测人脸数: 1 | 耗时: 0.136s
2025-07-24 23:03:57 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face5.jpg | faces=1 | time=0.136
2025-07-24 23:03:57 | wenzhou_face | INFO | logger_config.py:167 | 批量处理完成: 总数=8 | 成功=8 | 失败=0 | 总耗时=1.872s
2025-07-24 23:03:57 | wenzhou_face.performance | INFO | logger_config.py:171 | batch_total=8 | batch_success=8 | batch_failed=0 | batch_time=1.872
2025-07-24 23:03:57 | wenzhou_face | INFO | inference_engine.py:1043 | 结果已保存到: ../data/output/batch_results.json
2025-07-24 23:04:06 | wenzhou_face | INFO | logger_config.py:157 | 授权验证成功: License有效
2025-07-24 23:04:06 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.015s
2025-07-24 23:04:06 | wenzhou_face | INFO | logger_config.py:163 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 0.114s
2025-07-24 23:04:06 | wenzhou_face | INFO | inference_engine.py:819 | 温州人脸识别引擎初始化完成
2025-07-24 23:04:06 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face1.jpg
2025-07-24 23:04:06 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face1.jpg | 检测到 1 张人脸 | 耗时: 0.098s
2025-07-24 23:04:06 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face1.jpg | detect_faces=1 | detect_time=0.098
2025-07-24 23:04:06 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face1.jpg | 检测人脸数: 1 | 耗时: 0.157s
2025-07-24 23:04:06 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face1.jpg | faces=1 | time=0.157
2025-07-24 23:04:06 | wenzhou_face | INFO | logger_config.py:113 | 开始人脸识别: ../data/input/face3.jpg
2025-07-24 23:04:07 | wenzhou_face | INFO | logger_config.py:125 | 人脸检测: ../data/input/face3.jpg | 检测到 1 张人脸 | 耗时: 0.091s
2025-07-24 23:04:07 | wenzhou_face.performance | INFO | logger_config.py:129 | detect_image=face3.jpg | detect_faces=1 | detect_time=0.091
2025-07-24 23:04:07 | wenzhou_face | INFO | logger_config.py:117 | 人脸识别完成: ../data/input/face3.jpg | 检测人脸数: 1 | 耗时: 0.144s
2025-07-24 23:04:07 | wenzhou_face.performance | INFO | logger_config.py:121 | image=face3.jpg | faces=1 | time=0.144
2025-07-25 00:10:52 | wenzhou_face | INFO | logger_config.py:160 | 授权验证成功: License有效
2025-07-25 00:10:52 | wenzhou_face | INFO | inference_engine.py:146 | 选择的 ONNX Runtime providers: ['CoreMLExecutionProvider', 'CPUExecutionProvider']
2025-07-25 00:10:53 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.462s
2025-07-25 00:10:54 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 1.576s
2025-07-25 00:10:54 | wenzhou_face | INFO | inference_engine.py:931 | 温州人脸识别引擎初始化完成
2025-07-25 00:10:54 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face1.jpg
2025-07-25 00:10:54 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face1.jpg | 检测到 1 张人脸 | 耗时: 0.051s
2025-07-25 00:10:54 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face1.jpg | detect_faces=1 | detect_time=0.051
2025-07-25 00:10:54 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face1.jpg | 检测人脸数: 1 | 耗时: 0.062s
2025-07-25 00:10:54 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face1.jpg | faces=1 | time=0.062
2025-07-25 00:10:54 | wenzhou_face | INFO | inference_engine.py:1155 | 结果已保存到: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/data/output/face1_results.json
2025-07-25 12:32:08 | wenzhou_face | INFO | logger_config.py:160 | 授权验证成功: License有效
2025-07-25 12:32:08 | wenzhou_face | INFO | inference_engine.py:146 | 选择的 ONNX Runtime providers: ['CoreMLExecutionProvider', 'CPUExecutionProvider']
2025-07-25 12:32:08 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.734s
2025-07-25 12:32:10 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 1.736s
2025-07-25 12:32:10 | wenzhou_face | INFO | inference_engine.py:931 | 温州人脸识别引擎初始化完成
2025-07-25 12:32:10 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face1.jpg
2025-07-25 12:32:10 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face1.jpg | 检测到 1 张人脸 | 耗时: 0.152s
2025-07-25 12:32:10 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face1.jpg | detect_faces=1 | detect_time=0.152
2025-07-25 12:32:10 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face1.jpg | 检测人脸数: 1 | 耗时: 0.190s
2025-07-25 12:32:10 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face1.jpg | faces=1 | time=0.190
2025-07-25 12:32:10 | wenzhou_face | INFO | inference_engine.py:1155 | 结果已保存到: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/data/output/face1_results.json
2025-07-25 12:32:37 | wenzhou_face | INFO | logger_config.py:160 | 授权验证成功: License有效
2025-07-25 12:32:37 | wenzhou_face | INFO | inference_engine.py:146 | 选择的 ONNX Runtime providers: ['CoreMLExecutionProvider', 'CPUExecutionProvider']
2025-07-25 12:32:38 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.507s
2025-07-25 12:32:39 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 1.437s
2025-07-25 12:32:39 | wenzhou_face | INFO | inference_engine.py:931 | 温州人脸识别引擎初始化完成
2025-07-25 12:32:39 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face8.jpg
2025-07-25 12:32:39 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face8.jpg | 检测到 2 张人脸 | 耗时: 0.050s
2025-07-25 12:32:39 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face8.jpg | detect_faces=2 | detect_time=0.050
2025-07-25 12:32:39 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face8.jpg | 检测人脸数: 2 | 耗时: 0.067s
2025-07-25 12:32:39 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face8.jpg | faces=2 | time=0.067
2025-07-25 12:32:39 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face1.jpg
2025-07-25 12:32:39 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face1.jpg | 检测到 1 张人脸 | 耗时: 0.023s
2025-07-25 12:32:39 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face1.jpg | detect_faces=1 | detect_time=0.023
2025-07-25 12:32:39 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face1.jpg | 检测人脸数: 1 | 耗时: 0.027s
2025-07-25 12:32:39 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face1.jpg | faces=1 | time=0.027
2025-07-25 12:32:39 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face2.jpg
2025-07-25 12:32:39 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face2.jpg | 检测到 0 张人脸 | 耗时: 0.026s
2025-07-25 12:32:39 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face2.jpg | detect_faces=0 | detect_time=0.026
2025-07-25 12:32:39 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face2.jpg | 检测人脸数: 0 | 耗时: 0.028s
2025-07-25 12:32:39 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face2.jpg | faces=0 | time=0.028
2025-07-25 12:32:39 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face3.jpg
2025-07-25 12:32:39 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face3.jpg | 检测到 1 张人脸 | 耗时: 0.023s
2025-07-25 12:32:39 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face3.jpg | detect_faces=1 | detect_time=0.023
2025-07-25 12:32:39 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face3.jpg | 检测人脸数: 1 | 耗时: 0.033s
2025-07-25 12:32:39 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face3.jpg | faces=1 | time=0.033
2025-07-25 12:32:39 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face7.jpg
2025-07-25 12:32:39 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face7.jpg | 检测到 15 张人脸 | 耗时: 0.024s
2025-07-25 12:32:39 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face7.jpg | detect_faces=15 | detect_time=0.024
2025-07-25 12:32:40 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face7.jpg | 检测人脸数: 15 | 耗时: 0.069s
2025-07-25 12:32:40 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face7.jpg | faces=15 | time=0.069
2025-07-25 12:32:40 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face6.jpg
2025-07-25 12:32:40 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face6.jpg | 检测到 3 张人脸 | 耗时: 0.024s
2025-07-25 12:32:40 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face6.jpg | detect_faces=3 | detect_time=0.024
2025-07-25 12:32:40 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face6.jpg | 检测人脸数: 3 | 耗时: 0.035s
2025-07-25 12:32:40 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face6.jpg | faces=3 | time=0.035
2025-07-25 12:32:40 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face4.jpg
2025-07-25 12:32:40 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face4.jpg | 检测到 1 张人脸 | 耗时: 0.022s
2025-07-25 12:32:40 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face4.jpg | detect_faces=1 | detect_time=0.022
2025-07-25 12:32:40 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face4.jpg | 检测人脸数: 1 | 耗时: 0.027s
2025-07-25 12:32:40 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face4.jpg | faces=1 | time=0.027
2025-07-25 12:32:40 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face5.jpg
2025-07-25 12:32:40 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face5.jpg | 检测到 1 张人脸 | 耗时: 0.025s
2025-07-25 12:32:40 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face5.jpg | detect_faces=1 | detect_time=0.025
2025-07-25 12:32:40 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face5.jpg | 检测人脸数: 1 | 耗时: 0.029s
2025-07-25 12:32:40 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face5.jpg | faces=1 | time=0.029
2025-07-25 12:32:40 | wenzhou_face | INFO | logger_config.py:170 | 批量处理完成: 总数=8 | 成功=8 | 失败=0 | 总耗时=0.317s
2025-07-25 12:32:40 | wenzhou_face.performance | INFO | logger_config.py:174 | batch_total=8 | batch_success=8 | batch_failed=0 | batch_time=0.317
2025-07-25 12:32:40 | wenzhou_face | INFO | inference_engine.py:1155 | 结果已保存到: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/data/output/batch_results.json
2025-07-25 12:33:28 | wenzhou_face | INFO | logger_config.py:160 | 授权验证成功: License有效
2025-07-25 12:33:28 | wenzhou_face | INFO | inference_engine.py:146 | 选择的 ONNX Runtime providers: ['CoreMLExecutionProvider', 'CPUExecutionProvider']
2025-07-25 12:33:28 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.542s
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 1.595s
2025-07-25 12:33:30 | wenzhou_face | INFO | inference_engine.py:931 | 温州人脸识别引擎初始化完成
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face8.jpg
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face8.jpg | 检测到 2 张人脸 | 耗时: 0.051s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face8.jpg | detect_faces=2 | detect_time=0.051
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face8.jpg | 检测人脸数: 2 | 耗时: 0.070s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face8.jpg | faces=2 | time=0.070
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face1.jpg
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face1.jpg | 检测到 1 张人脸 | 耗时: 0.025s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face1.jpg | detect_faces=1 | detect_time=0.025
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face1.jpg | 检测人脸数: 1 | 耗时: 0.029s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face1.jpg | faces=1 | time=0.029
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face2.jpg
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face2.jpg | 检测到 0 张人脸 | 耗时: 0.022s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face2.jpg | detect_faces=0 | detect_time=0.022
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face2.jpg | 检测人脸数: 0 | 耗时: 0.023s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face2.jpg | faces=0 | time=0.023
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face3.jpg
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face3.jpg | 检测到 1 张人脸 | 耗时: 0.023s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face3.jpg | detect_faces=1 | detect_time=0.023
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face3.jpg | 检测人脸数: 1 | 耗时: 0.033s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face3.jpg | faces=1 | time=0.033
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face7.jpg
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face7.jpg | 检测到 15 张人脸 | 耗时: 0.023s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face7.jpg | detect_faces=15 | detect_time=0.023
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face7.jpg | 检测人脸数: 15 | 耗时: 0.068s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face7.jpg | faces=15 | time=0.068
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face6.jpg
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face6.jpg | 检测到 3 张人脸 | 耗时: 0.022s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face6.jpg | detect_faces=3 | detect_time=0.022
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face6.jpg | 检测人脸数: 3 | 耗时: 0.032s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face6.jpg | faces=3 | time=0.032
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face4.jpg
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face4.jpg | 检测到 1 张人脸 | 耗时: 0.022s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face4.jpg | detect_faces=1 | detect_time=0.022
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face4.jpg | 检测人脸数: 1 | 耗时: 0.027s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face4.jpg | faces=1 | time=0.027
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face5.jpg
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face5.jpg | 检测到 1 张人脸 | 耗时: 0.022s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face5.jpg | detect_faces=1 | detect_time=0.022
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face5.jpg | 检测人脸数: 1 | 耗时: 0.025s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face5.jpg | faces=1 | time=0.025
2025-07-25 12:33:30 | wenzhou_face | INFO | logger_config.py:170 | 批量处理完成: 总数=8 | 成功=8 | 失败=0 | 总耗时=0.311s
2025-07-25 12:33:30 | wenzhou_face.performance | INFO | logger_config.py:174 | batch_total=8 | batch_success=8 | batch_failed=0 | batch_time=0.311
2025-07-25 12:33:30 | wenzhou_face | INFO | inference_engine.py:1155 | 结果已保存到: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/data/output/batch_results.json
2025-07-25 12:34:26 | wenzhou_face | INFO | logger_config.py:160 | 授权验证成功: License有效
2025-07-25 12:34:26 | wenzhou_face | INFO | inference_engine.py:146 | 选择的 ONNX Runtime providers: ['CoreMLExecutionProvider', 'CPUExecutionProvider']
2025-07-25 12:34:26 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.556s
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 1.727s
2025-07-25 12:34:28 | wenzhou_face | INFO | inference_engine.py:931 | 温州人脸识别引擎初始化完成
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face8.jpg
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face8.jpg | 检测到 2 张人脸 | 耗时: 0.052s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face8.jpg | detect_faces=2 | detect_time=0.052
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face8.jpg | 检测人脸数: 2 | 耗时: 0.069s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face8.jpg | faces=2 | time=0.069
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face1.jpg
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face1.jpg | 检测到 1 张人脸 | 耗时: 0.026s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face1.jpg | detect_faces=1 | detect_time=0.026
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face1.jpg | 检测人脸数: 1 | 耗时: 0.030s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face1.jpg | faces=1 | time=0.030
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face2.jpg
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face2.jpg | 检测到 0 张人脸 | 耗时: 0.023s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face2.jpg | detect_faces=0 | detect_time=0.023
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face2.jpg | 检测人脸数: 0 | 耗时: 0.024s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face2.jpg | faces=0 | time=0.024
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face3.jpg
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face3.jpg | 检测到 1 张人脸 | 耗时: 0.021s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face3.jpg | detect_faces=1 | detect_time=0.021
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face3.jpg | 检测人脸数: 1 | 耗时: 0.030s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face3.jpg | faces=1 | time=0.030
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face7.jpg
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face7.jpg | 检测到 15 张人脸 | 耗时: 0.022s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face7.jpg | detect_faces=15 | detect_time=0.022
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face7.jpg | 检测人脸数: 15 | 耗时: 0.067s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face7.jpg | faces=15 | time=0.067
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face6.jpg
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face6.jpg | 检测到 3 张人脸 | 耗时: 0.023s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face6.jpg | detect_faces=3 | detect_time=0.023
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face6.jpg | 检测人脸数: 3 | 耗时: 0.042s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face6.jpg | faces=3 | time=0.042
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face4.jpg
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face4.jpg | 检测到 1 张人脸 | 耗时: 0.026s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face4.jpg | detect_faces=1 | detect_time=0.026
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face4.jpg | 检测人脸数: 1 | 耗时: 0.031s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face4.jpg | faces=1 | time=0.031
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: data/input/face5.jpg
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: data/input/face5.jpg | 检测到 1 张人脸 | 耗时: 0.024s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=face5.jpg | detect_faces=1 | detect_time=0.024
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: data/input/face5.jpg | 检测人脸数: 1 | 耗时: 0.029s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:124 | image=face5.jpg | faces=1 | time=0.029
2025-07-25 12:34:28 | wenzhou_face | INFO | logger_config.py:170 | 批量处理完成: 总数=8 | 成功=8 | 失败=0 | 总耗时=0.326s
2025-07-25 12:34:28 | wenzhou_face.performance | INFO | logger_config.py:174 | batch_total=8 | batch_success=8 | batch_failed=0 | batch_time=0.326
2025-07-25 12:34:28 | wenzhou_face | INFO | inference_engine.py:1155 | 结果已保存到: /Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithms/wenzhou_face/data/output/batch_results.json
2025-07-25 13:37:31 | wenzhou_face | INFO | api_server.py:165 | 正在初始化温州人脸识别引擎...
2025-07-25 13:37:31 | wenzhou_face | INFO | logger_config.py:160 | 授权验证成功: License有效
2025-07-25 13:37:31 | wenzhou_face | INFO | inference_engine.py:214 | 选择的 ONNX Runtime providers: ['CoreMLExecutionProvider', 'CPUExecutionProvider']
2025-07-25 13:37:32 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.648s
2025-07-25 13:37:33 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 1.720s
2025-07-25 13:37:33 | wenzhou_face | INFO | inference_engine.py:916 | 温州人脸识别引擎初始化完成
2025-07-25 13:37:33 | wenzhou_face | INFO | api_server.py:182 | 温州人脸识别引擎初始化完成
2025-07-25 13:37:52 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmp5xm_m56z.jpg
2025-07-25 13:37:52 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmp5xm_m56z.jpg | 检测到 1 张人脸 | 耗时: 0.088s
2025-07-25 13:37:52 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=tmp5xm_m56z.jpg | detect_faces=1 | detect_time=0.088
2025-07-25 13:37:52 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmp5xm_m56z.jpg | 检测人脸数: 1 | 耗时: 0.114s
2025-07-25 13:37:52 | wenzhou_face.performance | INFO | logger_config.py:124 | image=tmp5xm_m56z.jpg | faces=1 | time=0.114
2025-07-25 13:37:52 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmp8km9n0zd.jpg
2025-07-25 13:37:52 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmp8km9n0zd.jpg | 检测到 1 张人脸 | 耗时: 0.028s
2025-07-25 13:37:52 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=tmp8km9n0zd.jpg | detect_faces=1 | detect_time=0.028
2025-07-25 13:37:52 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmp8km9n0zd.jpg | 检测人脸数: 1 | 耗时: 0.031s
2025-07-25 13:37:52 | wenzhou_face.performance | INFO | logger_config.py:124 | image=tmp8km9n0zd.jpg | faces=1 | time=0.031
2025-07-25 13:37:52 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpkvrfsx95.jpg
2025-07-25 13:37:52 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpkvrfsx95.jpg | 检测到 1 张人脸 | 耗时: 0.024s
2025-07-25 13:37:52 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=tmpkvrfsx95.jpg | detect_faces=1 | detect_time=0.024
2025-07-25 13:37:52 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpkvrfsx95.jpg | 检测人脸数: 1 | 耗时: 0.027s
2025-07-25 13:37:52 | wenzhou_face.performance | INFO | logger_config.py:124 | image=tmpkvrfsx95.jpg | faces=1 | time=0.027
2025-07-25 13:38:07 | wenzhou_face | INFO | api_server.py:193 | 温州人脸识别API服务正在关闭...
2025-07-25 13:38:27 | wenzhou_face | INFO | api_server.py:165 | 正在初始化温州人脸识别引擎...
2025-07-25 13:38:27 | wenzhou_face | INFO | logger_config.py:160 | 授权验证成功: License有效
2025-07-25 13:38:27 | wenzhou_face | INFO | inference_engine.py:214 | 选择的 ONNX Runtime providers: ['CoreMLExecutionProvider', 'CPUExecutionProvider']
2025-07-25 13:38:28 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.492s
2025-07-25 13:38:30 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 1.592s
2025-07-25 13:38:30 | wenzhou_face | INFO | inference_engine.py:916 | 温州人脸识别引擎初始化完成
2025-07-25 13:38:30 | wenzhou_face | INFO | api_server.py:182 | 温州人脸识别引擎初始化完成
2025-07-25 13:38:47 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpmt7pmty3.jpg
2025-07-25 13:38:47 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpmt7pmty3.jpg | 检测到 1 张人脸 | 耗时: 0.086s
2025-07-25 13:38:47 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=tmpmt7pmty3.jpg | detect_faces=1 | detect_time=0.086
2025-07-25 13:38:47 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpmt7pmty3.jpg | 检测人脸数: 1 | 耗时: 0.108s
2025-07-25 13:38:47 | wenzhou_face.performance | INFO | logger_config.py:124 | image=tmpmt7pmty3.jpg | faces=1 | time=0.108
2025-07-25 13:38:47 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmp4fh6bexc.jpg
2025-07-25 13:38:47 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmp4fh6bexc.jpg | 检测到 1 张人脸 | 耗时: 0.027s
2025-07-25 13:38:47 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=tmp4fh6bexc.jpg | detect_faces=1 | detect_time=0.027
2025-07-25 13:38:47 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmp4fh6bexc.jpg | 检测人脸数: 1 | 耗时: 0.031s
2025-07-25 13:38:47 | wenzhou_face.performance | INFO | logger_config.py:124 | image=tmp4fh6bexc.jpg | faces=1 | time=0.031
2025-07-25 13:38:47 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpeaxsxkwq.jpg
2025-07-25 13:38:47 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpeaxsxkwq.jpg | 检测到 1 张人脸 | 耗时: 0.024s
2025-07-25 13:38:47 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=tmpeaxsxkwq.jpg | detect_faces=1 | detect_time=0.024
2025-07-25 13:38:47 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpeaxsxkwq.jpg | 检测人脸数: 1 | 耗时: 0.028s
2025-07-25 13:38:47 | wenzhou_face.performance | INFO | logger_config.py:124 | image=tmpeaxsxkwq.jpg | faces=1 | time=0.028
2025-07-25 13:41:02 | wenzhou_face | INFO | api_server.py:193 | 温州人脸识别API服务正在关闭...
2025-07-25 13:41:34 | wenzhou_face | INFO | api_server.py:165 | 正在初始化温州人脸识别引擎...
2025-07-25 13:41:34 | wenzhou_face | INFO | logger_config.py:160 | 授权验证成功: License有效
2025-07-25 13:41:34 | wenzhou_face | INFO | inference_engine.py:214 | 选择的 ONNX Runtime providers: ['CoreMLExecutionProvider', 'CPUExecutionProvider']
2025-07-25 13:41:34 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.564s
2025-07-25 13:41:36 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 1.673s
2025-07-25 13:41:36 | wenzhou_face | INFO | inference_engine.py:939 | 质量评估配置: enable_quality_check = False
2025-07-25 13:41:36 | wenzhou_face | INFO | inference_engine.py:952 | 质量评估功能未启用
2025-07-25 13:41:36 | wenzhou_face | INFO | inference_engine.py:916 | 温州人脸识别引擎初始化完成
2025-07-25 13:41:36 | wenzhou_face | INFO | api_server.py:182 | 温州人脸识别引擎初始化完成
2025-07-25 13:41:54 | wenzhou_face | INFO | api_server.py:193 | 温州人脸识别API服务正在关闭...
2025-07-25 13:42:41 | wenzhou_face | INFO | api_server.py:166 | 正在初始化温州人脸识别引擎...
2025-07-25 13:42:41 | wenzhou_face | INFO | api_server.py:182 | 使用配置文件: src/config/prod.ini
2025-07-25 13:42:41 | wenzhou_face | INFO | logger_config.py:160 | 授权验证成功: License有效
2025-07-25 13:42:41 | wenzhou_face | INFO | inference_engine.py:214 | 选择的 ONNX Runtime providers: ['CoreMLExecutionProvider', 'CPUExecutionProvider']
2025-07-25 13:42:42 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.463s
2025-07-25 13:42:43 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 1.591s
2025-07-25 13:42:43 | wenzhou_face | INFO | inference_engine.py:939 | 质量评估配置: enable_quality_check = False
2025-07-25 13:42:43 | wenzhou_face | INFO | inference_engine.py:952 | 质量评估功能未启用
2025-07-25 13:42:43 | wenzhou_face | INFO | inference_engine.py:916 | 温州人脸识别引擎初始化完成
2025-07-25 13:42:43 | wenzhou_face | INFO | api_server.py:184 | 温州人脸识别引擎初始化完成
2025-07-25 13:42:53 | wenzhou_face | INFO | api_server.py:195 | 温州人脸识别API服务正在关闭...
2025-07-25 13:43:16 | wenzhou_face | INFO | api_server.py:166 | 正在初始化温州人脸识别引擎...
2025-07-25 13:43:16 | wenzhou_face | INFO | api_server.py:182 | 使用配置文件: src/config/prod.ini
2025-07-25 13:43:16 | wenzhou_face | INFO | logger_config.py:160 | 授权验证成功: License有效
2025-07-25 13:43:16 | wenzhou_face | INFO | inference_engine.py:214 | 选择的 ONNX Runtime providers: ['CoreMLExecutionProvider', 'CPUExecutionProvider']
2025-07-25 13:43:17 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.467s
2025-07-25 13:43:19 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 1.545s
2025-07-25 13:43:19 | wenzhou_face | INFO | inference_engine.py:939 | 质量评估配置: enable_quality_check = True
2025-07-25 13:43:19 | wenzhou_face | INFO | inference_engine.py:944 | 正在加载质量评估模型: models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx
2025-07-25 13:43:19 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceQuality (models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx) | 耗时: 0.382s
2025-07-25 13:43:19 | wenzhou_face | INFO | inference_engine.py:950 | 质量评估模型加载完成
2025-07-25 13:43:19 | wenzhou_face | INFO | inference_engine.py:916 | 温州人脸识别引擎初始化完成
2025-07-25 13:43:19 | wenzhou_face | INFO | api_server.py:184 | 温州人脸识别引擎初始化完成
2025-07-25 13:43:31 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmp3b7qagls.jpg
2025-07-25 13:43:31 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmp3b7qagls.jpg | 检测到 1 张人脸 | 耗时: 0.072s
2025-07-25 13:43:31 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=tmp3b7qagls.jpg | detect_faces=1 | detect_time=0.072
2025-07-25 13:43:31 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmp3b7qagls.jpg | 检测人脸数: 1 | 耗时: 0.086s
2025-07-25 13:43:31 | wenzhou_face.performance | INFO | logger_config.py:124 | image=tmp3b7qagls.jpg | faces=1 | time=0.086
2025-07-25 13:43:31 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpeoo413bz.jpg
2025-07-25 13:43:31 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpeoo413bz.jpg | 检测到 1 张人脸 | 耗时: 0.027s
2025-07-25 13:43:31 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=tmpeoo413bz.jpg | detect_faces=1 | detect_time=0.027
2025-07-25 13:43:31 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpeoo413bz.jpg | 检测人脸数: 1 | 耗时: 0.030s
2025-07-25 13:43:31 | wenzhou_face.performance | INFO | logger_config.py:124 | image=tmpeoo413bz.jpg | faces=1 | time=0.030
2025-07-25 13:43:31 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmprfm8150p.jpg
2025-07-25 13:43:31 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmprfm8150p.jpg | 检测到 1 张人脸 | 耗时: 0.023s
2025-07-25 13:43:31 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=tmprfm8150p.jpg | detect_faces=1 | detect_time=0.023
2025-07-25 13:43:31 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmprfm8150p.jpg | 检测人脸数: 1 | 耗时: 0.026s
2025-07-25 13:43:31 | wenzhou_face.performance | INFO | logger_config.py:124 | image=tmprfm8150p.jpg | faces=1 | time=0.026
2025-07-25 13:43:31 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmp5f4_44pn.jpg
2025-07-25 13:43:31 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmp5f4_44pn.jpg | 检测到 1 张人脸 | 耗时: 0.024s
2025-07-25 13:43:31 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=tmp5f4_44pn.jpg | detect_faces=1 | detect_time=0.024
2025-07-25 13:43:31 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmp5f4_44pn.jpg | 检测人脸数: 1 | 耗时: 0.055s
2025-07-25 13:43:31 | wenzhou_face.performance | INFO | logger_config.py:124 | image=tmp5f4_44pn.jpg | faces=1 | time=0.055
2025-07-25 13:44:29 | wenzhou_face | INFO | api_server.py:195 | 温州人脸识别API服务正在关闭...
2025-07-25 13:44:38 | wenzhou_face | INFO | api_server.py:166 | 正在初始化温州人脸识别引擎...
2025-07-25 13:44:38 | wenzhou_face | INFO | api_server.py:182 | 使用配置文件: src/config/prod.ini
2025-07-25 13:44:38 | wenzhou_face | INFO | logger_config.py:160 | 授权验证成功: License有效
2025-07-25 13:44:38 | wenzhou_face | INFO | inference_engine.py:214 | 选择的 ONNX Runtime providers: ['CoreMLExecutionProvider', 'CPUExecutionProvider']
2025-07-25 13:44:38 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.448s
2025-07-25 13:44:40 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 1.652s
2025-07-25 13:44:40 | wenzhou_face | INFO | inference_engine.py:939 | 质量评估配置: enable_quality_check = True
2025-07-25 13:44:40 | wenzhou_face | INFO | inference_engine.py:944 | 正在加载质量评估模型: models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx
2025-07-25 13:44:40 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceQuality (models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx) | 耗时: 0.366s
2025-07-25 13:44:40 | wenzhou_face | INFO | inference_engine.py:950 | 质量评估模型加载完成
2025-07-25 13:44:40 | wenzhou_face | INFO | inference_engine.py:916 | 温州人脸识别引擎初始化完成
2025-07-25 13:44:40 | wenzhou_face | INFO | api_server.py:184 | 温州人脸识别引擎初始化完成
2025-07-25 13:44:54 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpb35iu8gg.jpg
2025-07-25 13:44:54 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpb35iu8gg.jpg | 检测到 1 张人脸 | 耗时: 0.072s
2025-07-25 13:44:54 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=tmpb35iu8gg.jpg | detect_faces=1 | detect_time=0.072
2025-07-25 13:44:54 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpb35iu8gg.jpg | 检测人脸数: 1 | 耗时: 0.079s
2025-07-25 13:44:54 | wenzhou_face.performance | INFO | logger_config.py:124 | image=tmpb35iu8gg.jpg | faces=1 | time=0.079
2025-07-25 13:44:54 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpy41bdncb.jpg
2025-07-25 13:44:54 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpy41bdncb.jpg | 检测到 1 张人脸 | 耗时: 0.025s
2025-07-25 13:44:54 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=tmpy41bdncb.jpg | detect_faces=1 | detect_time=0.025
2025-07-25 13:44:54 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpy41bdncb.jpg | 检测人脸数: 1 | 耗时: 0.029s
2025-07-25 13:44:54 | wenzhou_face.performance | INFO | logger_config.py:124 | image=tmpy41bdncb.jpg | faces=1 | time=0.029
2025-07-25 13:44:54 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpig_l9r9f.jpg
2025-07-25 13:44:54 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpig_l9r9f.jpg | 检测到 1 张人脸 | 耗时: 0.026s
2025-07-25 13:44:54 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=tmpig_l9r9f.jpg | detect_faces=1 | detect_time=0.026
2025-07-25 13:44:54 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpig_l9r9f.jpg | 检测人脸数: 1 | 耗时: 0.029s
2025-07-25 13:44:54 | wenzhou_face.performance | INFO | logger_config.py:124 | image=tmpig_l9r9f.jpg | faces=1 | time=0.029
2025-07-25 13:44:54 | wenzhou_face | INFO | logger_config.py:116 | 开始人脸识别: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpn7_9gncu.jpg
2025-07-25 13:44:54 | wenzhou_face | INFO | logger_config.py:128 | 人脸检测: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpn7_9gncu.jpg | 检测到 1 张人脸 | 耗时: 0.025s
2025-07-25 13:44:54 | wenzhou_face.performance | INFO | logger_config.py:132 | detect_image=tmpn7_9gncu.jpg | detect_faces=1 | detect_time=0.025
2025-07-25 13:44:54 | wenzhou_face | INFO | logger_config.py:120 | 人脸识别完成: /var/folders/pl/k87_q3lj4sz0rw403cnzhnbw0000gn/T/tmpn7_9gncu.jpg | 检测人脸数: 1 | 耗时: 0.044s
2025-07-25 13:44:54 | wenzhou_face.performance | INFO | logger_config.py:124 | image=tmpn7_9gncu.jpg | faces=1 | time=0.044
2025-07-25 13:45:09 | wenzhou_face | INFO | api_server.py:195 | 温州人脸识别API服务正在关闭...
2025-07-28 14:47:48 | wenzhou_face | INFO | api_server.py:166 | 正在初始化温州人脸识别引擎...
2025-07-28 14:47:48 | wenzhou_face | INFO | api_server.py:182 | 使用配置文件: src/config/prod.ini
2025-07-28 14:47:48 | wenzhou_face | INFO | logger_config.py:160 | 授权验证成功: License有效
2025-07-28 14:47:48 | wenzhou_face | INFO | inference_engine.py:214 | 选择的 ONNX Runtime providers: ['CoreMLExecutionProvider', 'CPUExecutionProvider']
2025-07-28 14:47:48 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.694s
2025-07-28 14:47:50 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 1.543s
2025-07-28 14:47:50 | wenzhou_face | INFO | inference_engine.py:939 | 质量评估配置: enable_quality_check = True
2025-07-28 14:47:50 | wenzhou_face | INFO | inference_engine.py:944 | 正在加载质量评估模型: models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx
2025-07-28 14:47:50 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceQuality (models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx) | 耗时: 0.392s
2025-07-28 14:47:50 | wenzhou_face | INFO | inference_engine.py:950 | 质量评估模型加载完成
2025-07-28 14:47:50 | wenzhou_face | INFO | inference_engine.py:916 | 温州人脸识别引擎初始化完成
2025-07-28 14:47:50 | wenzhou_face | INFO | api_server.py:184 | 温州人脸识别引擎初始化完成
2025-07-28 14:47:50 | wenzhou_face | INFO | api_server.py:195 | 温州人脸识别API服务正在关闭...
2025-07-28 14:48:03 | wenzhou_face | INFO | api_server.py:166 | 正在初始化温州人脸识别引擎...
2025-07-28 14:48:03 | wenzhou_face | INFO | api_server.py:182 | 使用配置文件: src/config/prod.ini
2025-07-28 14:48:03 | wenzhou_face | INFO | logger_config.py:160 | 授权验证成功: License有效
2025-07-28 14:48:03 | wenzhou_face | INFO | inference_engine.py:214 | 选择的 ONNX Runtime providers: ['CoreMLExecutionProvider', 'CPUExecutionProvider']
2025-07-28 14:48:03 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.458s
2025-07-28 14:48:05 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 1.518s
2025-07-28 14:48:05 | wenzhou_face | INFO | inference_engine.py:939 | 质量评估配置: enable_quality_check = True
2025-07-28 14:48:05 | wenzhou_face | INFO | inference_engine.py:944 | 正在加载质量评估模型: models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx
2025-07-28 14:48:05 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceQuality (models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx) | 耗时: 0.387s
2025-07-28 14:48:05 | wenzhou_face | INFO | inference_engine.py:950 | 质量评估模型加载完成
2025-07-28 14:48:05 | wenzhou_face | INFO | inference_engine.py:916 | 温州人脸识别引擎初始化完成
2025-07-28 14:48:05 | wenzhou_face | INFO | api_server.py:184 | 温州人脸识别引擎初始化完成
2025-07-28 14:48:05 | wenzhou_face | INFO | api_server.py:195 | 温州人脸识别API服务正在关闭...
2025-07-28 14:48:19 | wenzhou_face | INFO | api_server.py:166 | 正在初始化温州人脸识别引擎...
2025-07-28 14:48:19 | wenzhou_face | INFO | api_server.py:182 | 使用配置文件: src/config/prod.ini
2025-07-28 14:48:19 | wenzhou_face | INFO | logger_config.py:160 | 授权验证成功: License有效
2025-07-28 14:48:19 | wenzhou_face | INFO | inference_engine.py:214 | 选择的 ONNX Runtime providers: ['CoreMLExecutionProvider', 'CPUExecutionProvider']
2025-07-28 14:48:19 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceDetection (models/scrfd_10g_tykjanimal_240322.onnx) | 耗时: 0.412s
2025-07-28 14:48:20 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceRecognition (models/face_feature_Resnet50_zsq_20240201.onnx) | 耗时: 1.448s
2025-07-28 14:48:20 | wenzhou_face | INFO | inference_engine.py:939 | 质量评估配置: enable_quality_check = True
2025-07-28 14:48:20 | wenzhou_face | INFO | inference_engine.py:944 | 正在加载质量评估模型: models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx
2025-07-28 14:48:21 | wenzhou_face | INFO | logger_config.py:166 | 模型加载完成: FaceQuality (models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx) | 耗时: 0.374s
2025-07-28 14:48:21 | wenzhou_face | INFO | inference_engine.py:950 | 质量评估模型加载完成
2025-07-28 14:48:21 | wenzhou_face | INFO | inference_engine.py:916 | 温州人脸识别引擎初始化完成
2025-07-28 14:48:21 | wenzhou_face | INFO | api_server.py:184 | 温州人脸识别引擎初始化完成
2025-07-28 14:49:03 | wenzhou_face | INFO | api_server.py:195 | 温州人脸识别API服务正在关闭...
