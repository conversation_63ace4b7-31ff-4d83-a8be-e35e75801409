#!/bin/bash

# 温州人脸识别算法Docker启动脚本
# 解决授权服务连接问题的正确启动方式

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
IMAGE_NAME="wenzhou-face-api"
CONTAINER_NAME="wenzhou-face-test"
HOST_PORT="8001"
CONTAINER_PORT="8001"

echo -e "${BLUE}🚀 启动温州人脸识别算法服务${NC}"
echo ""

# 停止并删除现有容器
echo "🧹 清理现有容器..."
docker stop "$CONTAINER_NAME" 2>/dev/null || true
docker rm "$CONTAINER_NAME" 2>/dev/null || true

# 启动新容器
echo "🐳 启动Docker容器..."
docker run -d \
  --name "$CONTAINER_NAME" \
  --add-host=host.docker.internal:host-gateway \
  -p "$HOST_PORT:$CONTAINER_PORT" \
  -e "IN_DOCKER=1" \
  "$IMAGE_NAME:latest"

echo ""
echo -e "${GREEN}✅ 容器启动成功！${NC}"
echo ""
echo "📋 服务信息："
echo "   容器名称: $CONTAINER_NAME"
echo "   服务地址: http://localhost:$HOST_PORT"
echo "   API文档: http://localhost:$HOST_PORT/docs"
echo ""
echo "🔍 测试命令："
echo "   健康检查: curl http://localhost:$HOST_PORT/api/v1/health"
echo "   算法信息: curl http://localhost:$HOST_PORT/api/v1/info"
echo ""
echo "📝 查看日志："
echo "   docker logs -f $CONTAINER_NAME"
echo ""

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 3

# 检查服务状态
if curl -s --max-time 5 "http://localhost:$HOST_PORT/api/v1/health" >/dev/null 2>&1; then
    echo -e "${GREEN}🎉 服务启动成功，授权验证通过！${NC}"
else
    echo "⚠️  服务可能还在启动中，请稍等片刻后测试"
    echo "   或查看日志: docker logs $CONTAINER_NAME"
fi
